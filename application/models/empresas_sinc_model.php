<?php

class EleveCrmApiClient
{
    /**
     * A URL base da API.
     * @var string
     */
    private $baseUrl;

    /**
     * Sua chave de API.
     * @var string
     */
    private $apiKey;

    /**
     * Seu segredo de API.
     * @var string
     */
    private $apiSecret;

    /**
     * O token de acesso obtido após o login.
     * @var string|null
     */
    private $accessToken = null;

    /**
     * O tipo de token (geralmente 'Bearer').
     * @var string|null
     */
    private $tokenType = null;

    /**
     * Construtor da classe.
     *
     * @param string $apiKey Sua chave de API.
     * @param string $apiSecret Seu segredo de API.
     */
    public function __construct(string $apiKey, string $apiSecret)
    {
        $this->apiKey = $apiKey;
        $this->apiSecret = $apiSecret;
        $this->baseUrl = config_item('api_base_url');
    }

    /**
     * Realiza o login na API para obter o token de acesso.
     *
     * @return bool Retorna true em caso de sucesso, false em caso de falha.
     */
    public function login(): bool
    {
        $url = $this->baseUrl . '/login';

        $data = [
            'api_key' => $this->apiKey,
            'api_secret' => $this->apiSecret,
        ];

        // Inicia a sessão cURL
        $ch = curl_init($url);

        // Configura as opções do cURL para a requisição de login
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Retorna a resposta como string
        curl_setopt($ch, CURLOPT_POST, true); // Define o método como POST
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data)); // Envia os dados em formato JSON
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        // Executa a requisição
        $response = curl_exec($ch);

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Verifica se a requisição foi bem-sucedida
        if ($httpCode === 200) {
            $responseData = json_decode($response, true);

            if (isset($responseData['status']) && $responseData['status'] === 'success') {
                // Armazena o token e o tipo para uso futuro
                $this->accessToken = $responseData['data']['accessToken'];
                $this->tokenType = $responseData['data']['type'];
                return true;
            }
        }

        // Se algo deu errado, limpa as credenciais e retorna falha
        $this->accessToken = null;
        $this->tokenType = null;
        error_log("Falha no login da API EleveCRM. Resposta: " . $response);
        return false;
    }

    /**
     * Faz uma requisição autenticada para um endpoint da API.
     *
     * @param string $method O método HTTP (GET, POST, PUT, DELETE).
     * @param string $endpoint O endpoint da API a ser chamado (ex: '/leads/?limit=50').
     * @param array $data Dados a serem enviados no corpo da requisição (para POST, PUT).
     * @return array|null Os dados da resposta decodificados como um array associativo ou null em caso de erro.
     */
    public function makeRequest(string $method, string $endpoint, array $data = [])
    {
        // Se ainda não tiver um token, tenta fazer o login primeiro.
        if ($this->accessToken === null) {
            if (!$this->login()) {
                // Não foi possível fazer o login, então não pode continuar.
                echo "Erro: Falha ao autenticar na API. Verifique suas credenciais.\n";
                return null;
            }
        }

        $url = $this->baseUrl . $endpoint;

        $ch = curl_init();

        // Configura as opções comuns do cURL
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Authorization: ' . $this->tokenType . ' ' . $this->accessToken
        ]);

        // Configura o método HTTP específico
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                // Adiciona o Content-Type para POST/PUT
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'Authorization: ' . $this->tokenType . ' ' . $this->accessToken
                ]);
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'Authorization: ' . $this->tokenType . ' ' . $this->accessToken
                ]);
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
            case 'GET':
            default:
                // GET é o padrão, não precisa de configuração extra.
                break;
        }

        $response = curl_exec($ch);
        // var_dump($response, 'response'); exit;
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            echo "Erro no cURL: " . $error . "\n";
            return null;
        }

        // Se o token expirou (401 Unauthorized), tenta renovar e refazer a requisição.
        // NOTA: A API de exemplo não tem refresh token, então apenas tentamos o login novamente.
        if ($httpCode === 401) {
            echo "Token expirado ou inválido. Tentando autenticar novamente...\n";
            // Força um novo login na próxima requisição
            $this->accessToken = null;
            return $this->makeRequest($method, $endpoint, $data);
        }

        if ($httpCode >= 400) {
            echo "Erro na requisição API. Código: {$httpCode}. Resposta: {$response}\n";
            return null;
        }

        return json_decode($response, true);
    }
}


class Empresas_sinc_model extends MY_Model
{

    public $_table = 'empresas_sinc_log';

    public function __construct()
    {
        parent::__construct();

        $this->load->model('empresas_sinc_log_model');
    }

    public function empty_empresas()
    {
        $this->db->empty_table('empresas');
        $this->empresas_sinc_log_model->log('Limpando registros.');
    }

    public function get_oportunidades_empresas($importacao_completa = false)
    {
        $apiKey = config_item('apiKey');
        $apiSecret = config_item('apiSecret');

        $apiClient = new EleveCrmApiClient($apiKey, $apiSecret);

        $empresas = $apiClient->makeRequest('GET', '/suspects');

        if (!empty($empresas)) {
            foreach ($empresas['data'] as  $empresa) {

                $empresa = (object) $empresa;

                $data = [
                    'id_empresa' => $empresa->id,
                    'nome_fantasia' => $empresa->name,
                    'razao_social' => $empresa->legal_name,
                    'cnpj' => $empresa->document,
                    'cep' => $empresa->postal_code,
                    'endereco' => $empresa->address,
                    'bairro' => $empresa->neighborhood,
                    'cidade' => $empresa->city,
                    'uf' => $empresa->state,
                    'telefone' =>  $empresa->phone,
                    'data_abertura' => $empresa->openingDate,
                    'estado' => $empresa->state,
                    'cnpj_matriz' => $empresa->headquartersCnpj,
                    'site' => $empresa->website,
                    'id_segmento' =>  $empresa->segment_id,
                    'id_vendedor' =>  $empresa->seller_id,
                    'ativo' =>  $empresa->is_active,
                    'id_status_negociacao' =>   $empresa->negotiation_status_id,
                    'criado_em' => $empresa->created_at,
                    'origem' =>  $empresa->origin,
                    'id_nacionalidade' => $empresa->nationality_id,
                    'id_usuario' => $empresa->user_id,
                    'is_cliente' => $empresa->is_client,
                    'conta_atribuida' => $empresa->assigned_account,
                    'num_colaboradores' => $empresa->employee_count,
                    'faturamento_anual' => $empresa->annual_revenue,
                    'tipo' => 'cliente'
                ];

                $this->db->replace('empresas', $data);

                $contacts = $apiClient->makeRequest('GET', '/leads/byCompany/' . $empresa->id);

                $this->processarContatos($contacts, $empresa->id);
            }
        }
    }

    /**
     * Processa os contatos de uma empresa e os insere no banco de dados
     *
     * @param array $contacts Array de contatos retornado pela API
     * @param int $empresaId ID da empresa
     * @return void
     */
    private function processarContatos($contacts, $empresaId)
    {
        if (!empty($contacts)) {
            foreach ($contacts['data'] as $contact) {
                $contact = (object) $contact;
                $data = [
                    'id_usuario' => $contact->id_contato,
                    'id_grupo' => 0,
                    'id_perfil' => 1,
                    'nome' => $contact->name,
                    'email' => $contact->email,
                    'senha' => '$2y$10$ySdRdHD3mITepdowswNtIO0ZPrc6vWj886arnO4vnVembZzvQ6u/6', // Portaldwtax@01
                    'cargo' => $contact->position,
                    'receber_notificacoes' => 0,
                    'ativo' => $contact->is_active,
                    'tipo' => 'cliente',
                    'language' => 'portuguese'
                ];

                $this->db->replace('usuario', $data);

                // Inserir o relacionamento na tabela de relacionamentos 'rel_usuario_empresa'
                $rel_data = [
                    'id_usuario' => $contact->id_contato,
                    'id_empresa' => $empresaId
                ];

                $this->db->replace('rel_usuario_empresa', $rel_data);
            }
        }
    }

    public function get_clientes_prospects()
    {
        $this->elevecrmdb->select('
            e.id_empresa           AS id_empresa,
            e.nome_fantasia        AS nome_fantasia,
            e.cnpj                 AS cnpj,
            e.cnpj_matriz          AS cnpj_matriz,
            e.razao_social         AS razao_social,
            e.cep                  AS cep,
            e.endereco             AS endereco,
            e.bairro               AS bairro,
            e.cidade               AS cidade,
            e.uf                   AS uf,
            e.telefone             AS telefone,
            e.site                 AS site,
            e.id_segmento          AS id_segmento,
            e.ativo                AS ativo,
            e.id_status_negociacao AS id_status_negociacao,
            e.criado_em            AS criado_em,
            e.origem               AS origem,
            e.id_nacionalidade     AS id_nacionalidade,
            e.id_usuario           AS id_usuario,
            e.data_abertura        AS data_abertura,
            e.is_cliente           AS is_cliente,
            e.conta_atribuida      AS conta_atribuida,
            e.num_colaboradores    AS num_colaboradores,
            e.faturamento_anual    AS faturamento_anual,
            o.id_oportunidade      AS id_oportunidade,
            o.status               AS status
        ');

        $this->elevecrmdb->join('elv_oportunidade o', 'e.id_empresa = o.id_empresa', 'left');
        $this->elevecrmdb->where_in('e.id_status_relacionamento', ['1', '2', '12', '13', '14']);
        $this->elevecrmdb->where('e.cnpj IS NOT NULL', null, false);
        $this->elevecrmdb->where('e.ativo', 1);
        $this->elevecrmdb->group_by('e.cnpj');
        $empresas_query = $this->elevecrmdb->get('elv_cad_empresa e');

        $empresas = $empresas_query->result();

        if (!empty($empresas)) {
            foreach ($empresas as $empresa) {
                $data = array(
                    'id_empresa' => $empresa->id_empresa,
                    'nome_fantasia' => $empresa->nome_fantasia,
                    'razao_social' => $empresa->razao_social,
                    'cnpj' => $empresa->cnpj,
                    'cep' => $empresa->cep,
                    'endereco' => $empresa->endereco,
                    'bairro' => $empresa->bairro,
                    'cidade' => $empresa->cidade,
                    'uf' => $empresa->uf,
                    'telefone' => $empresa->telefone,
                    'data_abertura' => $empresa->data_abertura,
                    'estado' => $empresa->uf,
                    'cnpj_matriz' => $empresa->cnpj_matriz,
                    'site' => $empresa->site,
                    'id_segmento' => $empresa->id_segmento,
                    'id_vendedor' => $empresa->id_vendedor,
                    'ativo' => $empresa->ativo,
                    'id_status_negociacao' => $empresa->id_status_negociacao,
                    'criado_em' => $empresa->criado_em,
                    'origem' => $empresa->origem,
                    'id_nacionalidade' => $empresa->id_nacionalidade,
                    'id_usuario' => $empresa->id_usuario,
                    'is_cliente' => $empresa->is_cliente,
                    'conta_atribuida' => $empresa->conta_atribuida,
                    'num_colaboradores' => $empresa->num_colaboradores,
                    'faturamento_anual' => $empresa->faturamento_anual,
                    'tipo' => 'suspect'
                );
                $this->db->insert('empresas', $data);
            }

            $this->empresas_sinc_log_model->log('Inserindo (' . count($empresas) . ') suspects');
        }
    }

    public function populate_empresas_suspect()
    {
        // $this->elevecrmdb->where("NOT EXISTS(SELECT * FROM elv_oportunidade o WHERE o.id_empresa = e.id_empresa LIMIT 1)", false, false);
        // $this->elevecrmdb->where('is_cliente', 0);

        $this->elevecrmdb->select('
            e.id_empresa           AS id_empresa,
            e.nome_fantasia        AS nome_fantasia,
            e.cnpj                 AS cnpj,
            e.cnpj_matriz          AS cnpj_matriz,
            e.razao_social         AS razao_social,
            e.cep                  AS cep,
            e.endereco             AS endereco,
            e.bairro               AS bairro,
            e.cidade               AS cidade,
            e.uf                   AS uf,
            e.telefone             AS telefone,
            e.site                 AS site,
            e.id_segmento          AS id_segmento,
            e.ativo                AS ativo,
            e.id_status_negociacao AS id_status_negociacao,
            e.criado_em            AS criado_em,
            e.origem               AS origem,
            e.id_nacionalidade     AS id_nacionalidade,
            e.id_usuario           AS id_usuario,
            e.data_abertura        AS data_abertura,
            e.is_cliente           AS is_cliente,
            e.conta_atribuida      AS conta_atribuida,
            e.num_colaboradores    AS num_colaboradores,
            e.faturamento_anual    AS faturamento_anual,
            o.id_oportunidade      AS id_oportunidade,
            o.status               AS status
        ');

        $this->elevecrmdb->join('elv_oportunidade o', 'e.id_empresa = o.id_empresa', 'left');
        $this->elevecrmdb->where('o.id_oportunidade', NULL);
        // $this->elevecrmdb->where('o.status', 3);
        // $this->elevecrmdb->where('o.excluido', 0);
        // $this->elevecrmdb->where('e.ativo', 1);
        //remover antes de liberar em homologação
        //TODO
        // $this->elevecrmdb->where('e.id_conta', 9);
        $this->elevecrmdb->where('e.is_cliente', 0);
        $this->elevecrmdb->group_by('e.id_empresa');

        $empresas_query = $this->elevecrmdb->get('elv_cad_empresa e');

        $empresas = $empresas_query->result();

        if (!empty($empresas)) {
            foreach ($empresas as $empresa) {
                $data = array(
                    'id_empresa' => $empresa->id_empresa,
                    'nome_fantasia' => $empresa->nome_fantasia,
                    'razao_social' => $empresa->razao_social,
                    'cnpj' => $empresa->cnpj,
                    'cep' => $empresa->cep,
                    'endereco' => $empresa->endereco,
                    'bairro' => $empresa->bairro,
                    'cidade' => $empresa->cidade,
                    'uf' => $empresa->uf,
                    'telefone' => $empresa->telefone,
                    'data_abertura' => $empresa->data_abertura,
                    'estado' => $empresa->uf,
                    'cnpj_matriz' => $empresa->cnpj_matriz,
                    'site' => $empresa->site,
                    'id_segmento' => $empresa->id_segmento,
                    'id_vendedor' => $empresa->id_vendedor,
                    'ativo' => $empresa->ativo,
                    'id_status_negociacao' => $empresa->id_status_negociacao,
                    'criado_em' => $empresa->criado_em,
                    'origem' => $empresa->origem,
                    'id_nacionalidade' => $empresa->id_nacionalidade,
                    'id_usuario' => $empresa->id_usuario,
                    'is_cliente' => $empresa->is_cliente,
                    'conta_atribuida' => $empresa->conta_atribuida,
                    'num_colaboradores' => $empresa->num_colaboradores,
                    'faturamento_anual' => $empresa->faturamento_anual,
                    'tipo' => 'suspect'
                );
                $this->db->insert('empresas', $data);
            }

            $this->empresas_sinc_log_model->log('Inserindo (' . count($empresas) . ') suspects');
        }
    }

    public function populate_empresas_clientes()
    {
        $this->elevecrmdb->where('is_cliente', 1);
        $empresas_query = $this->elevecrmdb->get('elv_cad_empresa');

        $empresas = $empresas_query->result();

        if (!empty($empresas)) {
            foreach ($empresas as $empresa) {
                $data = array(
                    'id_empresa' => $empresa->id_empresa,
                    'nome_fantasia' => $empresa->nome_fantasia,
                    'razao_social' => $empresa->razao_social,
                    'cnpj' => $empresa->cnpj,
                    'cep' => $empresa->cep,
                    'endereco' => $empresa->endereco,
                    'bairro' => $empresa->bairro,
                    'cidade' => $empresa->cidade,
                    'uf' => $empresa->uf,
                    'telefone' => $empresa->telefone,
                    'data_abertura' => $empresa->data_abertura,
                    'estado' => $empresa->uf,
                    'cnpj_matriz' => $empresa->cnpj_matriz,
                    'site' => $empresa->site,
                    'id_segmento' => $empresa->id_segmento,
                    'id_vendedor' => $empresa->id_vendedor,
                    'ativo' => $empresa->ativo,
                    'id_status_negociacao' => $empresa->id_status_negociacao,
                    'criado_em' => $empresa->criado_em,
                    'origem' => $empresa->origem,
                    'id_nacionalidade' => $empresa->id_nacionalidade,
                    'id_usuario' => $empresa->id_usuario,
                    'is_cliente' => $empresa->is_cliente,
                    'conta_atribuida' => $empresa->conta_atribuida,
                    'num_colaboradores' => $empresa->num_colaboradores,
                    'faturamento_anual' => $empresa->faturamento_anual,
                    'tipo' => 'cliente'
                );
                $this->db->insert('empresas', $data);
            }
        }
        $this->empresas_sinc_log_model->log('Inserindo (' . count($empresas) . ') clientes');
    }

    /**
     * @method populate_empresas_prospect
     * Alteração em 19/07/2022:
     *  - Regra de negócio do insert modificada para os registros recebidos fixarem o valor em
     * is_cliente = 1 tipo para 'cliente'. Solicitação foi formalizada pelo time da Becomex/Danilo,
     * com o objetivo de todos os prospects tenham as mesmas caracteristicas de um cliente no Portal.
     */
    public function populate_empresas_prospect()
    {
        $this->elevecrmdb->select('
            e.id_empresa           AS id_empresa,
            e.nome_fantasia        AS nome_fantasia,
            e.cnpj                 AS cnpj,
            e.cnpj_matriz          AS cnpj_matriz,
            e.razao_social         AS razao_social,
            e.cep                  AS cep,
            e.endereco             AS endereco,
            e.bairro               AS bairro,
            e.cidade               AS cidade,
            e.uf                   AS uf,
            e.telefone             AS telefone,
            e.site                 AS site,
            e.id_segmento          AS id_segmento,
            e.ativo                AS ativo,
            e.id_status_negociacao AS id_status_negociacao,
            e.criado_em            AS criado_em,
            e.origem               AS origem,
            e.id_nacionalidade     AS id_nacionalidade,
            e.id_usuario           AS id_usuario,
            e.data_abertura        AS data_abertura,
            e.is_cliente           AS is_cliente,
            e.conta_atribuida      AS conta_atribuida,
            e.num_colaboradores    AS num_colaboradores,
            e.faturamento_anual    AS faturamento_anual,
            o.id_oportunidade      AS id_oportunidade,
            o.status               AS status
        ');

        $this->elevecrmdb->join('elv_cad_empresa e', 'e.id_empresa = o.id_empresa', 'inner');
        $this->elevecrmdb->where('o.status <> 3');
        $this->elevecrmdb->where('o.excluido', 0);
        $this->elevecrmdb->where('e.ativo', 1);
        $this->elevecrmdb->where('e.is_cliente', 0);
        $this->elevecrmdb->group_by('e.id_empresa');
        $empresas_query = $this->elevecrmdb->get('elv_oportunidade o');

        $empresas = $empresas_query->result();

        if (!empty($empresas)) {
            foreach ($empresas as $empresa) {
                $data = array(
                    'id_empresa' => $empresa->id_empresa,
                    'nome_fantasia' => $empresa->nome_fantasia,
                    'razao_social' => $empresa->razao_social,
                    'cnpj' => $empresa->cnpj,
                    'cep' => $empresa->cep,
                    'endereco' => $empresa->endereco,
                    'bairro' => $empresa->bairro,
                    'cidade' => $empresa->cidade,
                    'uf' => $empresa->uf,
                    'telefone' => $empresa->telefone,
                    'data_abertura' => $empresa->data_abertura,
                    'estado' => $empresa->uf,
                    'cnpj_matriz' => $empresa->cnpj_matriz,
                    'site' => $empresa->site,
                    'id_segmento' => $empresa->id_segmento,
                    'ativo' => $empresa->ativo,
                    'id_status_negociacao' => $empresa->id_status_negociacao,
                    'criado_em' => $empresa->criado_em,
                    'origem' => $empresa->origem,
                    'id_nacionalidade' => $empresa->id_nacionalidade,
                    'id_usuario' => $empresa->id_usuario,
                    'is_cliente' => 1,
                    'conta_atribuida' => $empresa->conta_atribuida,
                    'num_colaboradores' => $empresa->num_colaboradores,
                    'faturamento_anual' => $empresa->faturamento_anual,
                    'id_oportunidade' => $empresa->id_oportunidade,
                    'status' => $empresa->status,
                    'tipo' => 'cliente'
                );
                $this->db->insert('empresas', $data);
            }
        }
        $this->empresas_sinc_log_model->log('Inserindo (' . count($empresas) . ') prospects');
    }
}
